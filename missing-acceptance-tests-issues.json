[{"title": "Acceptance Test: World Information Query", "description": "**User Story:**\nAs a player, I want to query the world's configuration parameters so that I can understand the game rules and plan my strategy accordingly.\n\n**Test Scenarios:**\n1. **Basic World Query** - Query world info with valid robot name\n2. **Anonymous World Query** - Query world info with any/dummy robot name\n3. **Empty Robot Name** - Query world info with empty robot name\n4. **World Info Validation** - Verify all required fields are present and valid\n5. **Multiple Consecutive Queries** - Ensure consistent responses across multiple calls\n\n**Acceptance Criteria:**\n- [ ] Can request world information using \"world\" command\n- [ ] Response includes dimensions, visibility, reload time, repair time, mine time, max shields, max shots\n- [ ] World command works with any robot name (name is ignored)\n- [ ] Response follows protocol specification format\n- [ ] All numeric values are positive integers\n- [ ] Dimensions array has exactly 2 elements [width, height]\n\n**Test Class:** `WorldInformationTests.java`\n\n**Priority:** High\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, protocol-compliance, world-management", "labels": ["acceptance-test", "protocol-compliance", "world-management", "high-priority"]}, {"title": "Acceptance Test: Robot Backward Movement", "description": "**User Story:**\nAs a player, I want to move my robot backward so that I can retreat from danger or reposition strategically.\n\n**Test Scenarios:**\n1. **Basic Backward Movement** - Move backward 1-5 steps in open space\n2. **Backward to World Boundary** - Move backward until hitting world edge\n3. **Backward Movement Blocked by Obstacle** - Move backward into obstacle\n4. **Backward Movement with Zero Steps** - Test edge case with 0 steps\n5. **Backward Movement with Large Steps** - Test with steps > world size\n6. **Backward Movement from Different Orientations** - Test from all 4 directions\n7. **Backward Movement Partial Success** - Move 3 steps but blocked after 1\n\n**Acceptance Criteria:**\n- [ ] Robot can move backward specified number of steps\n- [ ] Movement respects world boundaries\n- [ ] Movement blocked by obstacles returns \"Obstructed\"\n- [ ] Robot position updated correctly\n- [ ] Response follows protocol specification\n- [ ] Zero steps movement returns \"Done\" without position change\n- [ ] Partial movement updates position to last valid step\n\n**Test Class:** `RobotMovementTests.java` (extend existing)\n\n**Priority:** High\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, movement, protocol-compliance", "labels": ["acceptance-test", "movement", "protocol-compliance", "high-priority"]}, {"title": "Acceptance Test: Robot Obstacle Collision", "description": "**User Story:**\nAs a player, I want my robot to be blocked by obstacles when moving so that the world physics are realistic.\n\n**Test Scenarios:**\n1. **Forward Movement into Obstacle** - Robot moves forward and hits obstacle\n2. **Backward Movement into Obstacle** - Robot moves backward and hits obstacle\n3. **Partial Movement Before Obstacle** - Robot moves 3 steps but blocked after 2\n4. **Adjacent to Obstacle Movement** - Robot starts next to obstacle, tries to move into it\n5. **Multiple Obstacles in Path** - Robot encounters multiple obstacles in sequence\n6. **Large Obstacle Collision** - Robot hits large rectangular obstacle\n7. **Corner Obstacle Navigation** - Robot tries to move around obstacle corners\n8. **Robot-to-Robot Collision** - Robot blocked by another robot\n\n**Acceptance Criteria:**\n- [ ] Forward movement stops at obstacles\n- [ ] Backward movement stops at obstacles\n- [ ] Robot position updated to last valid position before obstacle\n- [ ] Response indicates \"Obstructed\"\n- [ ] State reflects correct final position\n- [ ] Robots block each other's movement\n- [ ] Collision detection works for all obstacle shapes\n\n**Test Class:** `RobotObstacleTests.java`\n\n**Priority:** High\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, obstacles, collision-detection", "labels": ["acceptance-test", "obstacles", "collision-detection", "high-priority"]}, {"title": "Acceptance Test: Robot Pit Death", "description": "**User Story:**\nAs a player, I want my robot to die when it falls into a pit so that pits pose a real danger.\n\n**Test Scenarios:**\n1. **Forward Movement into Pit** - Robot moves forward and falls into pit\n2. **Backward Movement into Pit** - Robot moves backward and falls into pit\n3. **Single Step into Pit** - Robot takes one step directly into pit\n4. **Multiple Steps Ending in Pit** - Robot moves several steps, last one into pit\n5. **Large Pit Entry** - Robot enters large rectangular pit\n6. **Overlapping Pits** - Robot enters area with multiple overlapping pits\n7. **Dead Robot Command Rejection** - Try to command dead robot\n8. **Dead Robot Visibility** - Verify other robots cannot see dead robot\n9. **Pit at World Boundary** - Robot falls into pit at edge of world\n\n**Acceptance Criteria:**\n- [ ] Robot dies when moving into pit\n- [ ] Robot status becomes \"DEAD\"\n- [ ] Response indicates \"Fell\"\n- [ ] Dead robot removed from world\n- [ ] Dead robot cannot execute further commands\n- [ ] Dead robot invisible to other robots\n- [ ] Death occurs immediately upon pit entry\n\n**Test Class:** `RobotPitTests.java`\n\n**Priority:** High\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, pits, death-mechanics", "labels": ["acceptance-test", "pits", "death-mechanics", "high-priority"]}, {"title": "Acceptance Test: Robot Mine Damage", "description": "**User Story:**\nAs a player, I want my robot to take damage when stepping on mines so that mines pose a strategic threat.\n\n**Test Scenarios:**\n1. **Mine Hit with Sufficient Shields** - Robot with 5 shields steps on mine (survives with 2)\n2. **Mine Hit with Exact Shields** - Robot with 3 shields steps on mine (survives with 0)\n3. **Mine Hit with Insufficient Shields** - Robot with 2 shields steps on mine (dies)\n4. **Mine Hit with No Shields** - Robot with 0 shields steps on mine (dies)\n5. **Multiple Mine Hits** - Robot steps on multiple mines in sequence\n6. **Own Mine Damage** - Robot steps on mine it placed itself\n7. **Forward Movement into Mine** - Robot moves forward onto mine\n8. **Backward Movement into Mine** - Robot moves backward onto mine\n9. **Mine Damage During Combat** - Robot takes mine damage while already damaged\n\n**Acceptance Criteria:**\n- [ ] Robot takes exactly 3 hits when stepping on mine\n- [ ] Robot dies if shields insufficient (< 3)\n- [ ] Robot survives if shields sufficient (>= 3)\n- [ ] Response indicates \"Mine\"\n- [ ] Shield count updated correctly (shields - 3)\n- [ ] Own mines damage the robot that placed them\n- [ ] Mine damage is immediate upon contact\n\n**Test Class:** `RobotMineTests.java`\n\n**Priority:** High\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, mines, damage-system", "labels": ["acceptance-test", "mines", "damage-system", "high-priority"]}, {"title": "Acceptance Test: Robot Turning", "description": "**User Story:**\nAs a player, I want to turn my robot left and right so that I can change direction to explore different areas.\n\n**Test Scenarios:**\n1. **Turn Right from North** - NORTH → EAST → SOUTH → WEST → NORTH\n2. **Turn Left from North** - NORTH → WEST → SOUTH → EAST → NORTH\n3. **Multiple Right Turns** - Complete 360° rotation clockwise\n4. **Multiple Left Turns** - Complete 360° rotation counterclockwise\n5. **Mixed Turn Sequence** - Combination of left and right turns\n6. **Turn from Each Direction** - Test turning from all 4 starting directions\n7. **Invalid Turn Direction** - Test with invalid argument (not \"left\" or \"right\")\n8. **Turn During Different States** - Turn while NORMAL, after movement, etc.\n9. **Position Preservation** - Verify position unchanged after multiple turns\n\n**Acceptance Criteria:**\n- [ ] Robot can turn left (counterclockwise)\n- [ ] Robot can turn right (clockwise)\n- [ ] Direction updates correctly (NORTH→EAST→SOUTH→WEST→NORTH)\n- [ ] Position remains unchanged when turning\n- [ ] Response follows protocol specification\n- [ ] Invalid turn directions return error\n- [ ] Turning works from any starting direction\n\n**Test Class:** `RobotTurningTests.java`\n\n**Priority:** High\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, movement, orientation", "labels": ["acceptance-test", "movement", "orientation", "high-priority"]}, {"title": "Acceptance Test: Robot Weapon Firing", "description": "**User Story:**\nAs a player, I want to fire my robot's weapon at enemies so that I can eliminate threats and score kills.\n\n**Acceptance Criteria:**\n- [ ] Robot can fire weapon in facing direction\n- [ ] Shots consume ammunition\n- [ ] Hit detection works for robots in line of fire\n- [ ] Miss detection when no target hit\n- [ ] Response includes hit/miss status and target information\n- [ ] Shot distance respects robot configuration\n\n**Test Class:** `RobotCombatTests.java`\n\n**Priority:** High\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, combat, weapons", "labels": ["acceptance-test", "combat", "weapons", "high-priority"]}, {"title": "Acceptance Test: Robot Weapon Reload", "description": "**User Story:**\nAs a player, I want to reload my robot's weapon so that I can continue fighting after running out of ammunition.\n\n**Acceptance Criteria:**\n- [ ] Robot enters RELOADING status\n- [ ] Robot cannot move while reloading\n- [ ] Ammunition restored to maximum after reload time\n- [ ] Reload time respects world configuration\n- [ ] Status returns to NORMAL after reload\n\n**Test Class:** `RobotReloadTests.java`\n\n**Priority:** Medium\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, combat, reload-mechanics", "labels": ["acceptance-test", "combat", "reload-mechanics", "medium-priority"]}, {"title": "Acceptance Test: Robot Shield Repair", "description": "**User Story:**\nAs a player, I want to repair my robot's shields so that I can recover from damage and continue fighting.\n\n**Acceptance Criteria:**\n- [ ] Robot enters REPAIRING status\n- [ ] Robot cannot move while repairing\n- [ ] Shields restored to maximum after repair time\n- [ ] Repair time respects world configuration\n- [ ] Status returns to NORMAL after repair\n\n**Test Class:** `RobotRepairTests.java`\n\n**Priority:** Medium\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, defense, repair-mechanics", "labels": ["acceptance-test", "defense", "repair-mechanics", "medium-priority"]}, {"title": "Acceptance Test: Robot Mine Placement", "description": "**User Story:**\nAs a player, I want to place mines to create traps so that I can control territory and damage enemies.\n\n**Acceptance Criteria:**\n- [ ] Robot can place mines at current position\n- [ ] Robot enters SETMINE status during placement\n- [ ] Robot automatically moves forward 1 step after placing mine\n- [ ] Robot takes damage if blocked and steps on own mine\n- [ ] Mine placement time respects world configuration\n\n**Test Class:** `RobotMineTests.java` (extend)\n\n**Priority:** Medium\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, mines, tactical-warfare", "labels": ["acceptance-test", "mines", "tactical-warfare", "medium-priority"]}, {"title": "Acceptance Test: Robot Mine Detection", "description": "**User Story:**\nAs a player, I want to detect mines near my robot so that I can avoid stepping on them.\n\n**Acceptance Criteria:**\n- [ ] Mines visible within quarter of world visibility range\n- [ ] Look command returns mine objects with correct distance\n- [ ] Mine detection works in all directions\n- [ ] Mine objects have correct type \"MINE\"\n\n**Test Class:** `RobotLookTests.java` (extend existing)\n\n**Priority:** Medium\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, mines, detection, visibility", "labels": ["acceptance-test", "mines", "detection", "visibility", "medium-priority"]}, {"title": "Acceptance Test: Robot Configuration Validation", "description": "**User Story:**\nAs a player, I want to launch robots with different configurations so that I can use different strategies and robot types.\n\n**Acceptance Criteria:**\n- [ ] Can launch robots with different shield strengths\n- [ ] Can launch robots with different shot counts\n- [ ] Shot count affects shot distance (inverse relationship)\n- [ ] Shield strength limited by world maximum\n- [ ] Robots with mines cannot have guns (shot count 0)\n\n**Test Class:** `RobotConfigurationTests.java`\n\n**Priority:** Medium\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, configuration, robot-types", "labels": ["acceptance-test", "configuration", "robot-types", "medium-priority"]}, {"title": "Acceptance Test: Error Handling and Protocol Compliance", "description": "**User Story:**\nAs a player, I want clear error messages when commands fail so that I can understand what went wrong and correct my actions.\n\n**Test Scenarios:**\n1. **Invalid Command Name** - Send command \"shoot\" instead of \"fire\"\n2. **Misspelled Command** - Send command \"luanch\" instead of \"launch\"\n3. **Empty Command** - Send request with empty command field\n4. **Missing Arguments** - Send command requiring arguments with empty array\n5. **Too Many Arguments** - Send command with more arguments than expected\n6. **Wrong Argument Types** - Send string where integer expected\n7. **Malformed JSON** - Send invalid JSON syntax\n8. **Missing Required Fields** - Send request without \"robot\" or \"command\"\n9. **Non-existent Robot Commands** - Send commands for robot that doesn't exist\n10. **Server Internal Error Simulation** - Trigger server-side errors\n\n**Acceptance Criteria:**\n- [ ] Invalid command names return \"Unsupported command\"\n- [ ] Malformed arguments return \"Could not parse arguments\"\n- [ ] Server errors return \"An internal error has occurred\"\n- [ ] Robot not found errors are properly handled\n- [ ] All error responses follow protocol specification\n- [ ] Error messages are descriptive and helpful\n- [ ] Malformed JSON handled gracefully\n\n**Test Class:** `ProtocolErrorTests.java`\n\n**Priority:** High\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, error-handling, protocol-compliance", "labels": ["acceptance-test", "error-handling", "protocol-compliance", "high-priority"]}, {"title": "Acceptance Test: Concurrent Robot Operations", "description": "**User Story:**\nAs a player, I want multiple robots to operate simultaneously so that I can have realistic multi-player battles.\n\n**Test Scenarios:**\n1. **Simultaneous Robot Launch** - Launch 5 robots at the same time\n2. **Concurrent Movement** - Multiple robots moving simultaneously\n3. **Robot Collision During Movement** - Two robots trying to move to same position\n4. **Simultaneous Combat** - Multiple robots firing at same time\n5. **Mixed Operations** - Some robots moving, others firing, others repairing\n6. **Resource Contention** - Multiple robots competing for same space\n7. **State Consistency** - Verify world state remains consistent\n8. **Command Ordering** - Test command processing order with concurrent requests\n9. **Robot Interaction Chain** - Robot A hits Robot B which moves into Robot C\n10. **Stress Test** - 10+ robots performing various operations\n\n**Acceptance Criteria:**\n- [ ] Multiple robots can be launched simultaneously\n- [ ] Robots can interact with each other (combat, blocking)\n- [ ] World state remains consistent with multiple robots\n- [ ] Commands from different robots processed correctly\n- [ ] Robot collisions handled properly\n- [ ] No race conditions in command processing\n- [ ] Performance acceptable with multiple robots\n\n**Test Class:** `ConcurrentRobotTests.java`\n\n**Priority:** Medium\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, concurrency, multi-robot", "labels": ["acceptance-test", "concurrency", "multi-robot", "medium-priority"]}]