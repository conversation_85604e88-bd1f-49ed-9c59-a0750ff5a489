[{"title": "Acceptance Test: Robot Weapon Firing - Comprehensive Combat", "description": "**User Story:**\nAs a player, I want to fire my robot's weapon at enemies so that I can eliminate threats and score kills.\n\n**Test Scenarios:**\n1. **Direct Hit at Close Range** - Robot fires at target 1 step away\n2. **Direct Hit at Maximum Range** - Robot fires at target at max weapon range\n3. **Miss - No Target in Range** - Robot fires with no targets in line of sight\n4. **Miss - Target Beyond Range** - Robot fires at target beyond weapon range\n5. **Hit Through Multiple Robots** - Robot fires through line of robots\n6. **Blocked Shot by Obstacle** - Robot fires but shot blocked by obstacle\n7. **Fire with No Ammunition** - Robot tries to fire with 0 shots\n8. **Fire from Different Orientations** - Test firing in all 4 directions\n9. **Rapid Fire Sequence** - Robot fires multiple shots in succession\n10. **Fire at Moving Target** - Target moves between fire command and hit\n11. **Friendly Fire** - Robot accidentally hits allied robot\n12. **Kill Shot** - Robot fires shot that kills target (reduces shields to 0)\n\n**Acceptance Criteria:**\n- [ ] Robot can fire weapon in facing direction\n- [ ] Shots consume ammunition (shots count decreases)\n- [ ] Hit detection works for robots in line of fire\n- [ ] Miss detection when no target hit\n- [ ] Response includes hit/miss status and target information\n- [ ] Shot distance respects robot configuration\n- [ ] Cannot fire with 0 ammunition\n- [ ] Obstacles block shots\n- [ ] Hit damage reduces target shields\n\n**Test Class:** `RobotCombatTests.java`\n\n**Priority:** High\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, combat, weapons", "labels": ["acceptance-test", "combat", "weapons", "high-priority"]}, {"title": "Acceptance Test: Robot Weapon Reload - Ammunition Management", "description": "**User Story:**\nAs a player, I want to reload my robot's weapon so that I can continue fighting after running out of ammunition.\n\n**Test Scenarios:**\n1. **Full Reload from Empty** - Robot with 0 shots reloads to maximum\n2. **Partial Reload** - Robot with some shots reloads to maximum\n3. **Reload with Full Ammunition** - Robot with max shots tries to reload\n4. **Reload Time Validation** - Verify reload takes configured time\n5. **Movement During Reload** - Try to move robot while reloading\n6. **Combat During Reload** - Try to fire while reloading\n7. **Reload Interruption** - Test if reload can be interrupted\n8. **Multiple Reload Attempts** - Robot tries to reload multiple times\n9. **Reload Different Robot Types** - Test reload for different shot configurations\n10. **Reload Status Transitions** - NORMAL → RELOADING → NORMAL\n\n**Acceptance Criteria:**\n- [ ] Robot enters RELOADING status\n- [ ] Robot cannot move while reloading\n- [ ] Robot cannot fire while reloading\n- [ ] Ammunition restored to maximum after reload time\n- [ ] Reload time respects world configuration\n- [ ] Status returns to NORMAL after reload\n- [ ] Cannot reload if already at maximum ammunition\n\n**Test Class:** `RobotReloadTests.java`\n\n**Priority:** Medium\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, combat, reload-mechanics", "labels": ["acceptance-test", "combat", "reload-mechanics", "medium-priority"]}, {"title": "Acceptance Test: Robot Shield Repair - Defense Management", "description": "**User Story:**\nAs a player, I want to repair my robot's shields so that I can recover from damage and continue fighting.\n\n**Test Scenarios:**\n1. **Full Repair from Damaged** - Robot with 1 shield repairs to maximum\n2. **Repair with No Damage** - Robot with full shields tries to repair\n3. **Repair Time Validation** - Verify repair takes configured time\n4. **Movement During Repair** - Try to move robot while repairing\n5. **Combat During Repair** - Try to fire while repairing\n6. **Repair Interruption** - Test if repair can be interrupted\n7. **Repair Under Fire** - Robot takes damage while repairing\n8. **Multiple Repair Attempts** - Robot tries to repair multiple times\n9. **Repair Different Shield Configurations** - Test various shield strengths\n10. **Repair Status Transitions** - NORMAL → REPAIRING → NORMAL\n11. **Repair After Mine Damage** - Robot repairs after stepping on mine\n12. **Repair After Combat Damage** - Robot repairs after being shot\n\n**Acceptance Criteria:**\n- [ ] Robot enters REPAIRING status\n- [ ] Robot cannot move while repairing\n- [ ] Robot cannot fire while repairing\n- [ ] Shields restored to maximum after repair time\n- [ ] Repair time respects world configuration\n- [ ] Status returns to NORMAL after repair\n- [ ] Cannot repair if already at maximum shields\n- [ ] Vulnerable to damage while repairing\n\n**Test Class:** `RobotRepairTests.java`\n\n**Priority:** Medium\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, defense, repair-mechanics", "labels": ["acceptance-test", "defense", "repair-mechanics", "medium-priority"]}, {"title": "Acceptance Test: Robot Mine Placement - Tactical Warfare", "description": "**User Story:**\nAs a player, I want to place mines to create traps so that I can control territory and damage enemies.\n\n**Test Scenarios:**\n1. **Basic Mine Placement** - Robot places mine and moves forward\n2. **Mine Placement at World Boundary** - Robot places mine at edge, cannot move forward\n3. **Mine Placement Blocked by Obstacle** - Robot places mine, forward blocked by obstacle\n4. **Mine Placement Blocked by Robot** - Robot places mine, forward blocked by another robot\n5. **Mine Placement Time Validation** - Verify mine placement takes configured time\n6. **Multiple Mine Placement** - Robot places several mines in sequence\n7. **Mine Placement Different Orientations** - Place mines facing all 4 directions\n8. **Movement During Mine Placement** - Try to move robot while setting mine\n9. **Combat During Mine Placement** - Robot vulnerable while setting mine\n10. **Mine Placement Status Transitions** - NORMAL → SETMINE → NORMAL\n11. **Own Mine Stepping** - Robot steps on mine it just placed\n12. **Mine Robot Configuration** - Verify mine robots have 0 shots\n\n**Acceptance Criteria:**\n- [ ] Robot can place mines at current position\n- [ ] Robot enters SETMINE status during placement\n- [ ] Robot automatically moves forward 1 step after placing mine\n- [ ] Robot takes damage if blocked and steps on own mine\n- [ ] Mine placement time respects world configuration\n- [ ] Robot cannot move while setting mine\n- [ ] Robot shields disabled while setting mine\n- [ ] Mine robots cannot have weapons (shots = 0)\n\n**Test Class:** `RobotMineTests.java` (extend)\n\n**Priority:** Medium\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, mines, tactical-warfare", "labels": ["acceptance-test", "mines", "tactical-warfare", "medium-priority"]}, {"title": "Acceptance Test: Robot Mine Detection - Tactical Awareness", "description": "**User Story:**\nAs a player, I want to detect mines near my robot so that I can avoid stepping on them.\n\n**Test Scenarios:**\n1. **Mine Detection at Quarter Visibility** - Detect mine at exactly quarter visibility range\n2. **Mine Detection Beyond Range** - Mine just outside detection range (invisible)\n3. **Mine Detection All Directions** - Mines placed north, south, east, west\n4. **Multiple Mine Detection** - Several mines within detection range\n5. **Mine Detection with Obstacles** - Mines behind obstacles (should still be visible)\n6. **Own Mine Detection** - Robot detects mines it placed itself\n7. **Enemy Mine Detection** - Robot detects mines placed by other robots\n8. **Mine Detection Different Visibility Settings** - Test with various world visibility configs\n9. **Mine Detection from Different Positions** - Test detection from various robot positions\n10. **Mine Detection with Movement** - Robot moves and mine detection updates\n\n**Acceptance Criteria:**\n- [ ] Mines visible within quarter of world visibility range\n- [ ] Look command returns mine objects with correct distance\n- [ ] Mine detection works in all directions\n- [ ] Mine objects have correct type \"MINE\"\n- [ ] Mines beyond detection range are invisible\n- [ ] Detection range calculated correctly (visibility / 4, rounded down)\n- [ ] Own and enemy mines both detectable\n\n**Test Class:** `RobotLookTests.java` (extend existing)\n\n**Priority:** Medium\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, mines, detection, visibility", "labels": ["acceptance-test", "mines", "detection", "visibility", "medium-priority"]}, {"title": "Acceptance Test: Robot Configuration Validation - Robot Types", "description": "**User Story:**\nAs a player, I want to launch robots with different configurations so that I can use different strategies and robot types.\n\n**Test Scenarios:**\n1. **Sniper Robot** - High shot distance (1-2 shots), low shields\n2. **Tank Robot** - Low shot distance (4-5 shots), high shields\n3. **Balanced Robot** - Medium shots (3), medium shields\n4. **Mine Robot** - 0 shots, medium shields, can place mines\n5. **Maximum Shield Robot** - Robot with world maximum shield strength\n6. **Minimum Configuration Robot** - Robot with 1 shield, 1 shot\n7. **Invalid High Shields** - Try to launch robot with shields > world max\n8. **Invalid Shot Configuration** - Try to launch robot with invalid shot count\n9. **Shot Distance Relationship** - Verify inverse relationship (shots vs distance)\n10. **Configuration Persistence** - Verify robot keeps configuration after launch\n11. **Mixed Robot Types** - Launch multiple robots with different configurations\n12. **Configuration Limits** - Test boundary values for shields and shots\n\n**Acceptance Criteria:**\n- [ ] Can launch robots with different shield strengths\n- [ ] Can launch robots with different shot counts\n- [ ] Shot count affects shot distance (inverse relationship: 5 shots = 1 distance, 1 shot = 5 distance)\n- [ ] Shield strength limited by world maximum\n- [ ] Robots with mines cannot have guns (shot count 0)\n- [ ] Invalid configurations rejected with appropriate error\n- [ ] Robot configuration persists throughout game\n\n**Test Class:** `RobotConfigurationTests.java`\n\n**Priority:** Medium\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, configuration, robot-types", "labels": ["acceptance-test", "configuration", "robot-types", "medium-priority"]}, {"title": "Acceptance Test: World Boundary Detection - Edge Cases", "description": "**User Story:**\nAs a player, I want my robot to be stopped by world boundaries so that robots cannot leave the defined world space.\n\n**Test Scenarios:**\n1. **North Boundary from Center** - Robot at center moves north to edge\n2. **South Boundary from Center** - Robot at center moves south to edge\n3. **East Boundary from Center** - Robot at center moves east to edge\n4. **West Boundary from Center** - Robot at center moves west to edge\n5. **Corner Boundary Testing** - Robot at corner tries to move out in both directions\n6. **Large Step Boundary** - Robot tries to move 10 steps beyond boundary\n7. **Boundary Edge Detection** - Look command detects EDGE objects at correct distances\n8. **Boundary from Different World Sizes** - Test 1x1, 2x2, 5x5 worlds\n9. **Backward Movement to Boundary** - Robot moves backward to edge\n10. **Boundary Response Messages** - Verify correct \"At the X edge\" messages\n11. **Boundary with Obstacles** - Boundary detection with obstacles near edges\n12. **Boundary Coordinate Validation** - Verify position coordinates stay within bounds\n\n**Acceptance Criteria:**\n- [ ] Movement stopped at world edges\n- [ ] Look command detects EDGE objects at boundaries\n- [ ] Robot position clamped to valid world coordinates\n- [ ] Edge detection works for all directions\n- [ ] Response indicates boundary collision with correct direction\n- [ ] Boundary detection consistent across different world sizes\n- [ ] Corner boundaries handled correctly\n- [ ] Edge distance calculations accurate\n\n**Test Class:** `WorldBoundaryTests.java`\n\n**Priority:** High\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, boundaries, world-limits", "labels": ["acceptance-test", "boundaries", "world-limits", "high-priority"]}, {"title": "Acceptance Test: Robot Death and Cleanup - Game State Management", "description": "**User Story:**\nAs a player, I want dead robots to be removed from the world so that the game state remains clean and accurate.\n\n**Test Scenarios:**\n1. **Death by Pit Fall** - Robot dies by falling into pit, verify cleanup\n2. **Death by Mine Explosion** - Robot dies from mine damage, verify cleanup\n3. **Death by Combat** - Robot dies from weapon fire, verify cleanup\n4. **Death by Multiple Mine Hits** - Robot dies from stepping on multiple mines\n5. **Command Rejection After Death** - Try to send commands to dead robot\n6. **Dead Robot Invisibility** - Other robots cannot see dead robot\n7. **Dead Robot Position Cleanup** - Dead robot position becomes available\n8. **Multiple Robot Deaths** - Several robots die simultaneously\n9. **Death During Combat** - Robot dies while in middle of combat\n10. **Death Status Persistence** - Dead robot status remains DEAD\n11. **Resurrection Prevention** - Dead robot cannot be revived\n12. **Death Notification** - Other robots notified of death events\n\n**Acceptance Criteria:**\n- [ ] Dead robots have status \"DEAD\"\n- [ ] Dead robots removed from world immediately\n- [ ] Dead robots cannot execute commands\n- [ ] Other robots cannot see dead robots\n- [ ] Dead robot cleanup is immediate and complete\n- [ ] Dead robot positions become available for other robots\n- [ ] Death events properly logged/notified\n- [ ] No zombie robots (dead but still active)\n\n**Test Class:** `RobotDeathTests.java`\n\n**Priority:** Medium\n**Epic:** Robot World Acceptance Tests\n**Labels:** acceptance-test, death-mechanics, cleanup", "labels": ["acceptance-test", "death-mechanics", "cleanup", "medium-priority"]}]