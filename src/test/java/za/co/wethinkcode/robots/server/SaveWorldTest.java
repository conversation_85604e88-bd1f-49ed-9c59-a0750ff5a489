package za.co.wethinkcode.robots.server;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.server.db.DatabaseConnection;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.Socket;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import static org.junit.jupiter.api.Assertions.*;

public class SaveWorldTest {
    private static Server server;
    private static Thread serverThread;
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final int PORT = 5000;

    @BeforeAll
    static void setUp() throws SQLException {
        DatabaseConnection.initializeDatabase();
        server = new Server(PORT, 10, null);
        serverThread = new Thread(server::start);
        serverThread.start();
        try {
            Thread.sleep(1000); // Wait for server to start
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @AfterAll
    static void tearDown() {
        Server.shutdown();
        try {
            serverThread.join(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Test
    void testSaveWorldCommand() throws Exception {
        try (Socket socket = new Socket("localhost", PORT);
             PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
             BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()))) {

            // Send save command
            String request = "{\"robot\":\"testRobot\",\"command\":\"save\",\"arguments\":[\"testWorld\"]}";
            out.println(request);
            String responseStr = in.readLine();
            JsonNode response = objectMapper.readTree(responseStr);

            // Verify response
            assertEquals("OK", response.get("result").asText());
            assertEquals("World 'testWorld' saved successfully", response.get("data").get("message").asText());

            // Verify database state
            try (Connection conn = DatabaseConnection.getConnection();
                 PreparedStatement pstmt = conn.prepareStatement("SELECT * FROM worlds WHERE name = ?")) {
                pstmt.setString(1, "testWorld");
                ResultSet rs = pstmt.executeQuery();
                assertTrue(rs.next(), "World should be saved");
                assertEquals("testWorld", rs.getString("name"));
                assertEquals(11, rs.getInt("width")); // World size 10 -> 11 (odd number adjustment)
                assertEquals(11, rs.getInt("height"));
            }
        }
    }

    @Test
    void testSaveWorldInvalidArguments() throws Exception {
        try (Socket socket = new Socket("localhost", PORT);
             PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
             BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()))) {

            // Send save command with no arguments
            String request = "{\"robot\":\"testRobot\",\"command\":\"save\",\"arguments\":[]}";
            out.println(request);
            String responseStr = in.readLine();
            JsonNode response = objectMapper.readTree(responseStr);

            // Verify response
            assertEquals("ERROR", response.get("result").asText());
            assertEquals("Invalid arguments: Usage: save <worldName>", response.get("data").get("message").asText());
        }
    }

    @Test
    void testSaveWorldEmptyName() throws Exception {
        try (Socket socket = new Socket("localhost", PORT);
             PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
             BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()))) {

            // Send save command with empty name
            String request = "{\"robot\":\"testRobot\",\"command\":\"save\",\"arguments\":[\" \"]}";
            out.println(request);
            String responseStr = in.readLine();
            JsonNode response = objectMapper.readTree(responseStr);

            // Verify response
            assertEquals("ERROR", response.get("result").asText());
            assertEquals("Invalid world name: Name cannot be empty", response.get("data").get("message").asText());
        }
    }
}
