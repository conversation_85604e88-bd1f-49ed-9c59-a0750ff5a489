package za.co.wethinkcode.robots.server.db;

import za.co.wethinkcode.robots.server.World;
import za.co.wethinkcode.robots.server.Obstacle;
import za.co.wethinkcode.robots.server.ObstacleType;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class WorldDAOImpl implements WorldDAO {
    @Override
    public void saveWorld(String worldName, World world) throws SQLException {
        try (Connection conn = DatabaseConnection.getConnection()) {
            conn.setAutoCommit(false); // Start transaction

            // Save world dimensions
            try (PreparedStatement pstmt = conn.prepareStatement(
                    "INSERT OR REPLACE INTO worlds (name, width, height) VALUES (?, ?, ?)")) {
                pstmt.setString(1, worldName);
                pstmt.setInt(2, world.getWidth());
                pstmt.setInt(3, world.getHeight());
                pstmt.executeUpdate();
            }

            // Clear existing obstacles for this world
            try (PreparedStatement pstmt = conn.prepareStatement(
                    "DELETE FROM obstacles WHERE world_name = ?")) {
                pstmt.setString(1, worldName);
                pstmt.executeUpdate();
            }

            // Save obstacles
            try (PreparedStatement pstmt = conn.prepareStatement(
                    "INSERT INTO obstacles (world_name, type, x, y, width, height) VALUES (?, ?, ?, ?, ?, ?)")) {
                for (Obstacle obstacle : world.getObstacles()) {
                    pstmt.setString(1, worldName);
                    pstmt.setString(2, obstacle.type().name());
                    pstmt.setInt(3, obstacle.getX());
                    pstmt.setInt(4, obstacle.getY());
                    pstmt.setInt(5, obstacle.getMaxX() - obstacle.getX());
                    pstmt.setInt(6, obstacle.getMaxY() - obstacle.getY());
                    pstmt.executeUpdate();
                }
            }

            conn.commit(); // Commit transaction
        } catch (SQLException e) {
            throw new SQLException("Failed to save world '" + worldName + "': " + e.getMessage(), e);
        }
    }

    public World restoreWorld(String worldName) throws SQLException {
        try (Connection conn = DatabaseConnection.getConnection()) {
            int width, height;
            try (PreparedStatement pstmt = conn.prepareStatement(
                    "SELECT width, height FROM worlds WHERE name = ?")) {
                pstmt.setString(1, worldName);
                try (ResultSet rs = pstmt.executeQuery()) {
                    if (!rs.next()) {
                        throw new SQLException("World '" + worldName + "' not found in database.");
                    }
                    width = rs.getInt("width");
                    height = rs.getInt("height");
                }
            }

            World world = new World(width, height);
            
            try (PreparedStatement pstmt = conn.prepareStatement(
                    "SELECT type, x, y, width, height FROM obstacles WHERE world_name = ?")) {
                pstmt.setString(1, worldName);
                try (ResultSet rs = pstmt.executeQuery()) {
                    while (rs.next()) {
                        String typeStr = rs.getString("type");
                        int x = rs.getInt("x");
                        int y = rs.getInt("y");
                        int w = rs.getInt("width");
                        int h = rs.getInt("height");

                        ObstacleType type = ObstacleType.valueOf(typeStr);
                        Obstacle obstacle = new Obstacle(type, x, y, w, h);
                        world.addObstacle(obstacle);
                    }
                }
            }

            return world;

        } catch (SQLException e) {
            throw new SQLException("Failed to restore world '" + worldName + "': " + e.getMessage(), e);
        }
    }
}