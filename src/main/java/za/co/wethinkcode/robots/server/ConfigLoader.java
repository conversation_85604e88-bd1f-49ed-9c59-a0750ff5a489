package za.co.wethinkcode.robots.server;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
/**
 * Loads configuration properties from a resource file and applies them to the World instance.
 * Handles missing or invalid files by falling back to default world settings.
 */
public class ConfigLoader {


    public Properties loadConfig(String resourcePath) throws IOException {
        Properties properties = new Properties();

        try (InputStream input = getClass().getClassLoader().getResourceAsStream(resourcePath)) {
            if (input == null) {
                throw new FileNotFoundException("Property file not found in the classpath");
            }
            properties.load(input);
        }
        return properties;
    }

    public String loadServerUrl(String resourcePath) throws IOException {
        Properties properties = new Properties();

        try (InputStream input = getClass().getClassLoader().getResourceAsStream(resourcePath)) {
            if (input == null) {
                throw new FileNotFoundException("Property file not found in the classpath");
            }
            properties.load(input);
        }
        System.out.println(properties.getProperty("server_url"));
        return properties.getProperty("server_url");
    }

    public void applyConfigToWorld(World world, String resourcePath) {
        System.out.println("Loading configuration...");
        try {
            Properties properties = loadConfig(resourcePath);
            world.setDimensions(
                    Integer.parseInt(properties.getProperty("world.width")),
                    Integer.parseInt(properties.getProperty("world.height"))
            );
            world.setWorldProperties(
                    Integer.parseInt(properties.getProperty("shield_repair_time")),
                    Integer.parseInt(properties.getProperty("weapon_reload_time")),
                    Integer.parseInt(properties.getProperty("max_shield_strength")),
                    Integer.parseInt(properties.getProperty("visibility"))
            );
            System.out.println("World successfully loaded with dimensions: " + world.getWidth() + " x " + world.getHeight());
        } catch (IOException e) {
            world.setDefaultDimensions();
            world.setDefaultWorldProperties();
            System.err.println("Could not load configuration: " + e.getMessage());
        }
    }

    public void applyPropertiesToWorld(World world, String resourcePath) {
        System.out.println("Loading configuration properties...");
        try {
            Properties properties = loadConfig(resourcePath);
            world.setWorldProperties(
                    Integer.parseInt(properties.getProperty("shield_repair_time")),
                    Integer.parseInt(properties.getProperty("weapon_reload_time")),
                    Integer.parseInt(properties.getProperty("max_shield_strength")),
                    Integer.parseInt(properties.getProperty("visibility"))
            );
            System.out.println("World properties loaded successfully. Dimensions: " + world.getWidth() + " x " + world.getHeight());
        } catch (IOException e) {
            world.setDefaultWorldProperties();
            System.err.println("Could not load configuration properties: " + e.getMessage());
        }
    }
}